import 'api.dart';
import '../services/auth.dart';

enum OrderStatus {
  pending,
  processing,
  shipped,
  delivered,
  cancelled;

  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'قيد الانتظار';
      case OrderStatus.processing:
        return 'قيد التحضير';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  static OrderStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return OrderStatus.pending;
      case 'processing':
        return OrderStatus.processing;
      case 'shipped':
        return OrderStatus.shipped;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return OrderStatus.pending;
    }
  }
}

class OrdersApiService {
  /// Create a new order
  static Future<OrderResponse> createOrder(CreateOrderRequest request) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response =
          await HttpService.instance.post('/api/v2/orders/', request.toJson());
      return OrderResponse.fromJson(response);
    } catch (e) {
      print('Error creating order: $e');
      rethrow;
    }
  }

  /// Get all orders for the authenticated user
  static Future<PaginatedOrderResponse> getMyOrders({
    int offset = 0,
    int limit = 20,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await HttpService.instance
          .get('/api/v2/orders/my?offset=$offset&limit=$limit');
      return PaginatedOrderResponse.fromJson(response);
    } catch (e) {
      print('Error getting my orders: $e');
      rethrow;
    }
  }

  /// Get orders for a specific store
  static Future<PaginatedOrderResponse> getOrders({
    required int storeId,
    int offset = 0,
    int limit = 10,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await HttpService.instance
          .get('/api/v2/orders/store/$storeId?offset=$offset&limit=$limit');
      return PaginatedOrderResponse.fromJson(response);
    } catch (e) {
      print('Error getting orders: $e');
      rethrow;
    }
  }

  /// Get a specific order by ID
  static Future<OrderResponse> getOrderById({
    required int storeId,
    required int orderId,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await HttpService.instance
          .get('/api/v2/orders/stores/$storeId/$orderId');
      return OrderResponse.fromJson(response);
    } catch (e) {
      print('Error getting order: $e');
      rethrow;
    }
  }

  /// Update order status
  static Future<OrderResponse> updateOrderStatus({
    required int storeId,
    required int orderId,
    required String status,
    String? statusReason,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await HttpService.instance.put(
        '/api/v2/orders/stores/$storeId/$orderId/status',
        {
          'status': status,
          'status_reason': statusReason,
        },
      );
      return OrderResponse.fromJson(response);
    } catch (e) {
      print('Error updating order status: $e');
      rethrow;
    }
  }

  /// Cancel an order
  static Future<void> cancelOrder({
    required int storeId,
    required int orderId,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      await HttpService.instance
          .delete('/api/v2/orders/stores/$storeId/$orderId');
    } catch (e) {
      print('Error cancelling order: $e');
      rethrow;
    }
  }
}

/// Data Models

class OrderItemRequest {
  final int itemId;
  final int quantity;

  OrderItemRequest({
    required this.itemId,
    required this.quantity,
  });

  Map<String, dynamic> toJson() {
    return {
      'item_id': itemId,
      'quantity': quantity,
    };
  }
}

class CreateOrderRequest {
  final int wholesalerId;
  final int storeId;
  final List<OrderItemRequest> items;
  final String? deliverAt;

  CreateOrderRequest({
    required this.wholesalerId,
    required this.storeId,
    required this.items,
    this.deliverAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'wholesaler_id': wholesalerId,
      'store_id': storeId,
      'items': items.map((item) => item.toJson()).toList(),
      'deliver_at': deliverAt,
    };
  }
}

class CustomUserResponse {
  final int id;
  final String username;
  final String email;
  final String firstName;
  final String lastName;

  CustomUserResponse({
    required this.id,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
  });

  factory CustomUserResponse.fromJson(Map<String, dynamic> json) {
    return CustomUserResponse(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      firstName: json['first_name'],
      lastName: json['last_name'],
    );
  }

  String get fullName => '$firstName $lastName'.trim();
}

class ProductInfo {
  final int id;
  final String name;
  final String title;
  final String barcode;
  final String slug;
  final String description;
  final String? imageUrl;
  final String unit;
  final int unitCount;

  ProductInfo({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    required this.unit,
    required this.unitCount,
  });

  factory ProductInfo.fromJson(Map<String, dynamic> json) {
    return ProductInfo(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      barcode: json['barcode'],
      slug: json['slug'],
      description: json['description'],
      imageUrl: json['image_url'],
      unit: json['unit'],
      unitCount: json['unit_count'],
    );
  }
}

class OrderItemResponse {
  final int id;
  final ProductInfo product;
  final int quantity;
  final double pricePerUnit;
  final double totalPrice;

  OrderItemResponse({
    required this.id,
    required this.product,
    required this.quantity,
    required this.pricePerUnit,
    required this.totalPrice,
  });

  factory OrderItemResponse.fromJson(Map<String, dynamic> json) {
    return OrderItemResponse(
      id: json['id'],
      product: ProductInfo.fromJson(json['product']),
      quantity: json['quantity'],
      pricePerUnit: (json['price_per_unit'] as num).toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
    );
  }
}

class OrderResponse {
  final int id;
  final int wholesalerId;
  final int storeId;
  final String storeName;
  final double totalPrice;
  final double fees;
  final String? deliverAt;
  final double productsTotalPrice;
  final int productsTotalQuantity;
  final OrderStatus status;
  final String? statusReason;
  final String statusUpdatedAt;
  final CustomUserResponse statusUpdatedBy;
  final String createdAt;
  final String updatedAt;
  final List<OrderItemResponse> orderItems;

  OrderResponse({
    required this.id,
    required this.wholesalerId,
    required this.storeId,
    required this.storeName,
    required this.totalPrice,
    required this.fees,
    this.deliverAt,
    required this.productsTotalPrice,
    required this.productsTotalQuantity,
    required this.status,
    this.statusReason,
    required this.statusUpdatedAt,
    required this.statusUpdatedBy,
    required this.createdAt,
    required this.updatedAt,
    required this.orderItems,
  });

  factory OrderResponse.fromJson(Map<String, dynamic> json) {
    return OrderResponse(
      id: json['id'],
      wholesalerId: json['wholesaler_id'],
      storeId: json['store_id'],
      storeName: json['store_name'],
      totalPrice: (json['total_price'] as num).toDouble(),
      fees: (json['fees'] as num).toDouble(),
      deliverAt: json['deliver_at'],
      productsTotalPrice: (json['products_total_price'] as num).toDouble(),
      productsTotalQuantity: json['products_total_quantity'],
      status: OrderStatus.fromString(json['status']),
      statusReason: json['status_reason'],
      statusUpdatedAt: json['status_updated_at'],
      statusUpdatedBy: CustomUserResponse.fromJson(json['status_updated_by']),
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      orderItems: (json['order_items'] as List)
          .map((item) => OrderItemResponse.fromJson(item))
          .toList(),
    );
  }

  /// Get formatted date from ISO string
  String get formattedDate {
    try {
      final date = DateTime.parse(createdAt);
      return '${date.day} ${_getMonthName(date.month)}';
    } catch (e) {
      return 'Invalid Date';
    }
  }

  String _getMonthName(int month) {
    const months = [
      '',
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return months[month];
  }

  /// Check if order is running (not completed or cancelled)
  bool get isRunning {
    return status != OrderStatus.delivered && status != OrderStatus.cancelled;
  }

  /// Check if order is completed
  bool get isCompleted {
    return status == OrderStatus.delivered || status == OrderStatus.cancelled;
  }
}

class PaginatedOrderResponse {
  final int total;
  final List<OrderResponse> items;

  PaginatedOrderResponse({
    required this.total,
    required this.items,
  });

  factory PaginatedOrderResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedOrderResponse(
      total: json['total'],
      items: (json['items'] as List)
          .map((order) => OrderResponse.fromJson(order))
          .toList(),
    );
  }
}
