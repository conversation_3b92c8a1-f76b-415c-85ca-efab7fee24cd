import 'package:dio/dio.dart';

class HttpService {
  HttpService._();

  final Dio _dio = Dio();

  static final _instance = HttpService._();

  static HttpService get instance => _instance;

  final String baseUrl = 'http://10.0.2.2:8000';

  HttpService();

  void setToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  void clearToken() {
    _dio.options.headers.remove('Authorization');
  }

  Future<dynamic> get(String url) async {
    final response = await _dio.get(baseUrl + url);
    return response.data;
  }

  Future<dynamic> post(String url, dynamic data) async {
    final response = await _dio.post(baseUrl + url, data: data);
    return response.data;
  }

  Future<dynamic> put(String url, dynamic data) async {
    final response = await _dio.put(baseUrl + url, data: data);
    return response.data;
  }

  Future<dynamic> delete(String url) async {
    final response = await _dio.delete(baseUrl + url);
    return response.data;
  }

  // Fetch categories and companies
  Future<Map<String, dynamic>> getCategoriesAndCompanies() async {
    final response = await get('/api/v2/categories-companies/');
    return response as Map<String, dynamic>;
  }
}
