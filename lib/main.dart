import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

import 'core/routes/app_routes.dart';
import 'core/routes/on_generate_route.dart';
import 'core/themes/app_themes.dart';
import 'services/app_services.dart';
import 'services/cart_service.dart';
import 'services/region_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize global services
  await AppServices().initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<CartService>.value(
          value: AppServices().cartService,
        ),
        ChangeNotifierProvider<RegionService>.value(
          value: AppServices().regionService,
        ),
      ],
      child: MaterialApp(
        title: 'Tager Plus',
        theme: AppTheme.defaultTheme,
        onGenerateRoute: RouteGenerator.onGenerate,
        initialRoute: AppRoutes.splash,
        locale: const Locale('ar', 'EG'),
        supportedLocales: const [
          Locale('ar', 'EG'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        localeResolutionCallback: (locale, supportedLocales) {
          return const Locale('ar', 'EG');
        },
      ),
    );
  }
}
