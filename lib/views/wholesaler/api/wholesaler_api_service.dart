import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../api/api.dart';
import '../models/wholesaler_models.dart' as models;

/// Exception classes for error handling
class WholesalerApiException implements Exception {
  final String message;
  final int? statusCode;

  WholesalerApiException(this.message, [this.statusCode]);

  @override
  String toString() => 'WholesalerApiException: $message';
}

/// Comprehensive API service for wholesaler operations
class WholesalerApiService {
  static const String _baseEndpoint = '/api/wholesalers/wholesalers';

  // ============================================================================
  // WHOLESALER PROFILE OPERATIONS
  // ============================================================================

  /// Create a new wholesaler store
  static Future<models.WholesalerProfile> createWholesaler(
      models.WholesalerCreateRequest request) async {
    try {
      final response = await HttpService.instance.post(
        _baseEndpoint,
        request.toJson(),
      );
      return models.WholesalerProfile.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get the current user's wholesaler store
  static Future<models.WholesalerProfile> getMyWholesaler() async {
    try {
      final response = await HttpService.instance.get('$_baseEndpoint/me');
      return models.WholesalerProfile.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Update the current user's wholesaler store
  static Future<models.WholesalerProfile> updateMyWholesaler(
      models.WholesalerUpdateRequest request) async {
    try {
      final response = await HttpService.instance.put(
        '$_baseEndpoint/me',
        request.toJson(),
      );
      return models.WholesalerProfile.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Upload logo for the wholesaler
  static Future<models.WholesalerProfile> uploadLogo(String filePath) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath),
      });

      final response = await HttpService.instance.post(
        '$_baseEndpoint/me/logo',
        formData,
      );
      return models.WholesalerProfile.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Upload background image for the wholesaler
  static Future<models.WholesalerProfile> uploadBackground(
      String filePath) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath),
      });

      final response = await HttpService.instance.post(
        '$_baseEndpoint/me/background',
        formData,
      );
      return models.WholesalerProfile.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  // ============================================================================
  // REGION MIN CHARGE OPERATIONS
  // ============================================================================

  /// Set minimum charge for a specific region
  static Future<models.RegionMinCharge> createMinCharge(
      models.RegionMinChargeRequest request) async {
    try {
      final response = await HttpService.instance.post(
        '$_baseEndpoint/me/min-charges',
        request.toJson(),
      );
      return models.RegionMinCharge.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// List all minimum charges for the current wholesaler
  static Future<List<models.RegionMinCharge>> listMinCharges() async {
    try {
      final response =
          await HttpService.instance.get('$_baseEndpoint/me/min-charges');

      if (response is List) {
        return response
            .map((item) => models.RegionMinCharge.fromJson(item))
            .toList();
      } else {
        throw WholesalerApiException('Invalid response format');
      }
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  // ============================================================================
  // ITEM MANAGEMENT OPERATIONS
  // ============================================================================

  /// Add a product with base pricing to the wholesaler's inventory
  static Future<models.WholesalerItem> createItem(
      models.WholesalerItemCreateRequest request) async {
    try {
      final response = await HttpService.instance.post(
        '$_baseEndpoint/me/items',
        request.toJson(),
      );
      return models.WholesalerItem.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// List all products in the wholesaler's inventory
  static Future<List<models.WholesalerItem>> listItems() async {
    try {
      final response =
          await HttpService.instance.get('$_baseEndpoint/me/items');

      if (response is List) {
        return response
            .map((item) => models.WholesalerItem.fromJson(item))
            .toList();
      } else {
        throw WholesalerApiException('Invalid response format');
      }
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get a specific product from the wholesaler's inventory
  static Future<models.WholesalerItem> getItem(int itemId) async {
    try {
      final response =
          await HttpService.instance.get('$_baseEndpoint/me/items/$itemId');
      return models.WholesalerItem.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Update a product's base pricing and inventory in the wholesaler's inventory
  static Future<models.WholesalerItem> updateItem(
      int itemId, models.WholesalerItemUpdateRequest request) async {
    try {
      final response = await HttpService.instance.put(
        '$_baseEndpoint/me/items/$itemId',
        request.toJson(),
      );
      return models.WholesalerItem.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Remove a product from the wholesaler's inventory (soft delete)
  static Future<void> deleteItem(int itemId) async {
    try {
      await HttpService.instance.delete('$_baseEndpoint/me/items/$itemId');
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Immediately expire a product's pricing
  static Future<void> expireItem(int itemId) async {
    try {
      await HttpService.instance
          .put('$_baseEndpoint/me/items/$itemId/expire', {});
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  // ============================================================================
  // INVENTORY MANAGEMENT OPERATIONS
  // ============================================================================

  /// Add or remove inventory from an item
  static Future<models.InventoryUpdateResponse> updateInventory(
      int itemId, models.InventoryTransactionRequest request) async {
    try {
      final response = await HttpService.instance.post(
        '$_baseEndpoint/me/items/$itemId/inventory',
        request.toJson(),
      );
      return models.InventoryUpdateResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// List all inventory transactions for an item
  static Future<List<models.InventoryTransaction>> listInventoryTransactions(
      int itemId) async {
    try {
      final response = await HttpService.instance
          .get('$_baseEndpoint/me/items/$itemId/inventory');

      if (response is List) {
        return response
            .map((item) => models.InventoryTransaction.fromJson(item))
            .toList();
      } else {
        throw WholesalerApiException('Invalid response format');
      }
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  // ============================================================================
  // ERROR HANDLING
  // ============================================================================

  /// Handle API errors and convert them to user-friendly messages
  static WholesalerApiException _handleApiError(DioException e) {
    String message;
    int? statusCode = e.response?.statusCode;

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        message = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
        break;
      case DioExceptionType.badResponse:
        switch (statusCode) {
          case 400:
            message = 'طلب غير صحيح. يرجى التحقق من البيانات المدخلة.';
            break;
          case 401:
            message = 'يجب تسجيل الدخول للوصول إلى هذه المعلومات.';
            break;
          case 403:
            message = 'ليس لديك صلاحية للوصول إلى هذه المعلومات.';
            break;
          case 404:
            message = 'المورد المطلوب غير موجود.';
            break;
          case 500:
            message = 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
            break;
          default:
            message = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
        }
        break;
      case DioExceptionType.cancel:
        message = 'تم إلغاء الطلب.';
        break;
      case DioExceptionType.badCertificate:
        message = 'خطأ في شهادة الأمان.';
        break;
      case DioExceptionType.connectionError:
        message = 'خطأ في الاتصال. يرجى التحقق من اتصال الإنترنت.';
        break;
      default:
        message = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }

    return WholesalerApiException(message, statusCode);
  }

  // ============================================================================
  // COMPANY AND PRODUCT SELECTION OPERATIONS
  // ============================================================================

  /// Get list of companies with optional search
  static Future<List<models.Company>> getCompanies({String? search}) async {
    try {
      String url = '/api/companies';
      if (search != null && search.isNotEmpty) {
        url += '?search=${Uri.encodeComponent(search)}';
      }

      final response = await HttpService.instance.get(url);

      if (response is List) {
        return response.map((json) => models.Company.fromJson(json)).toList();
      } else {
        throw WholesalerApiException('Invalid response format for companies');
      }
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException('خطأ في تحميل الشركات: ${e.toString()}');
    }
  }

  /// Get list of products with optional search and company filter
  static Future<List<models.Product>> getProducts({
    String? search,
    int? companyId,
    int page = 1,
    int size = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'size': size.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      if (companyId != null) {
        queryParams['company_id'] = companyId.toString();
      }

      String url = '/api/products/';
      if (queryParams.isNotEmpty) {
        final queryString = queryParams.entries
            .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
            .join('&');
        url += '?$queryString';
      }

      final response = await HttpService.instance.get(url);

      if (response is Map<String, dynamic> && response.containsKey('items')) {
        final items = response['items'] as List;
        return items.map((json) => models.Product.fromJson(json)).toList();
      } else {
        throw WholesalerApiException('Invalid response format for products');
      }
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException('خطأ في تحميل المنتجات: ${e.toString()}');
    }
  }

  /// Get categories and companies in a single call
  static Future<Map<String, List<dynamic>>> getCategoriesAndCompanies() async {
    try {
      final response = await HttpService.instance.get(
        '/api/v2/categories-companies/',
      );

      if (response is Map<String, dynamic>) {
        final categories = (response['categories'] as List?)
                ?.map((json) => models.Category.fromJson(json))
                .toList() ??
            [];

        final companies = (response['companies'] as List?)
                ?.map((json) => models.Company.fromJson(json))
                .toList() ??
            [];

        return {
          'categories': categories,
          'companies': companies,
        };
      } else {
        throw WholesalerApiException(
            'Invalid response format for categories and companies');
      }
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerApiException(
          'خطأ في تحميل الفئات والشركات: ${e.toString()}');
    }
  }
}
