import '../../../../api/auth/user.dart';
import '../../models/wholesaler_models.dart';

/// Combined profile data containing both user and wholesaler information
class WholesalerProfileData {
  final UserData userData;
  final WholesalerProfile wholesalerData;

  const WholesalerProfileData({
    required this.userData,
    required this.wholesalerData,
  });

  WholesalerProfileData copyWith({
    UserData? userData,
    WholesalerProfile? wholesalerData,
  }) {
    return WholesalerProfileData(
      userData: userData ?? this.userData,
      wholesalerData: wholesalerData ?? this.wholesalerData,
    );
  }
}

/// Request model for updating profile data
class ProfileUpdateRequest {
  final UserUpdateRequest? userUpdate;
  final WholesalerUpdateRequest? wholesalerUpdate;

  const ProfileUpdateRequest({
    this.userUpdate,
    this.wholesalerUpdate,
  });

  bool get hasUserUpdate => userUpdate != null;
  bool get hasWholesalerUpdate => wholesalerUpdate != null;
  bool get isEmpty => !hasUserUpdate && !hasWholesalerUpdate;
}

/// Form data for profile editing
class ProfileFormData {
  // User fields
  String firstName;
  String lastName;
  String email;
  String phone;

  // Wholesaler fields
  String title;
  String username;
  String category;

  ProfileFormData({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.title,
    required this.username,
    required this.category,
  });

  factory ProfileFormData.fromProfileData(WholesalerProfileData data) {
    return ProfileFormData(
      firstName: data.userData.firstName ?? '',
      lastName: data.userData.lastName ?? '',
      email: data.userData.email ?? '',
      phone: data.userData.phone,
      title: data.wholesalerData.title,
      username: data.wholesalerData.username,
      category: data.wholesalerData.category ?? '',
    );
  }

  /// Convert form data to update requests
  ProfileUpdateRequest toUpdateRequest(WholesalerProfileData originalData) {
    UserUpdateRequest? userUpdate;
    WholesalerUpdateRequest? wholesalerUpdate;

    // Check if user data changed
    if (firstName != (originalData.userData.firstName ?? '') ||
        lastName != (originalData.userData.lastName ?? '') ||
        email != (originalData.userData.email ?? '') ||
        phone != originalData.userData.phone) {
      userUpdate = UserUpdateRequest(
        firstName: firstName.isEmpty ? null : firstName,
        lastName: lastName.isEmpty ? null : lastName,
        email: email.isEmpty ? null : email,
        phone: phone.isEmpty ? null : phone,
      );
    }

    // Check if wholesaler data changed
    if (title != originalData.wholesalerData.title ||
        username != originalData.wholesalerData.username ||
        category != originalData.wholesalerData.category) {
      wholesalerUpdate = WholesalerUpdateRequest(
        title: title,
        username: username,
        category: category,
      );
    }

    return ProfileUpdateRequest(
      userUpdate: userUpdate,
      wholesalerUpdate: wholesalerUpdate,
    );
  }

  /// Validate form data
  Map<String, String> validate() {
    final errors = <String, String>{};

    if (firstName.trim().isEmpty) {
      errors['firstName'] = 'الاسم الأول مطلوب';
    }

    if (lastName.trim().isEmpty) {
      errors['lastName'] = 'الاسم الأخير مطلوب';
    }

    if (email.trim().isNotEmpty && !_isValidEmail(email)) {
      errors['email'] = 'البريد الإلكتروني غير صحيح';
    }

    if (phone.trim().isEmpty) {
      errors['phone'] = 'رقم الهاتف مطلوب';
    } else if (!_isValidPhone(phone)) {
      errors['phone'] = 'رقم الهاتف غير صحيح';
    }

    if (title.trim().isEmpty) {
      errors['title'] = 'اسم المتجر مطلوب';
    }

    if (username.trim().isEmpty) {
      errors['username'] = 'اسم المستخدم مطلوب';
    } else if (!_isValidUsername(username)) {
      errors['username'] = 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
    }

    if (category.trim().isEmpty) {
      errors['category'] = 'فئة المتجر مطلوبة';
    }

    return errors;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPhone(String phone) {
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(phone);
  }

  bool _isValidUsername(String username) {
    return RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username);
  }
}

/// Available store categories
class StoreCategories {
  static const List<String> categories = [
    'بقالة',
    'خضروات وفواكه',
    'لحوم ودواجن',
    'منتجات ألبان',
    'مخبوزات',
    'مواد تنظيف',
    'أدوات منزلية',
    'أخرى',
  ];

  static String getDisplayName(String category) {
    return categories.contains(category) ? category : 'أخرى';
  }
}
