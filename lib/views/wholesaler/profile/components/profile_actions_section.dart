import 'package:flutter/material.dart';
import '../../../../core/constants/constants.dart';

class ProfileActionsSection extends StatelessWidget {
  final VoidCallback onEditProfile;
  final VoidCallback onLogout;
  final bool isUpdating;

  const ProfileActionsSection({
    super.key,
    required this.onEditProfile,
    required this.onLogout,
    required this.isUpdating,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: AppDefaults.borderRadius,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.settings,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'الإعدادات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Edit Profile Action
            _buildActionTile(
              context,
              icon: Icons.edit,
              title: 'تعديل الملف الشخصي',
              subtitle: 'تحديث المعلومات الشخصية ومعلومات المتجر',
              onTap: isUpdating ? null : onEditProfile,
              trailing: isUpdating
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.arrow_forward_ios, size: 16),
            ),

            const Divider(),

            // Settings Action
            _buildActionTile(
              context,
              icon: Icons.settings_outlined,
              title: 'إعدادات التطبيق',
              subtitle: 'إعدادات الإشعارات واللغة',
              onTap: () {
                // TODO: Navigate to settings page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('الإعدادات قريباً')),
                );
              },
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            ),

            const Divider(),

            // Help & Support Action
            _buildActionTile(
              context,
              icon: Icons.help_outline,
              title: 'المساعدة والدعم',
              subtitle: 'الأسئلة الشائعة والتواصل مع الدعم',
              onTap: () {
                // TODO: Navigate to help page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('المساعدة قريباً')),
                );
              },
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            ),

            const Divider(),

            // Logout Action
            _buildActionTile(
              context,
              icon: Icons.logout,
              title: 'تسجيل الخروج',
              subtitle: 'الخروج من الحساب',
              onTap: onLogout,
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              isDestructive: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    final color = isDestructive ? Colors.red : Colors.black;
    final iconColor = isDestructive ? Colors.red : AppColors.primary;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDefaults.borderRadius / 2),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.placeholder,
                        ),
                  ),
                ],
              ),
            ),
            if (trailing != null) ...[
              const SizedBox(width: 8),
              trailing,
            ],
          ],
        ),
      ),
    );
  }
}
