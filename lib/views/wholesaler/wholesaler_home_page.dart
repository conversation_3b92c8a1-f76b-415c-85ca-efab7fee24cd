import 'package:flutter/material.dart';
import '../../core/constants/constants.dart';
import '../../services/auth.dart';
import '../../core/routes/app_routes.dart';
import 'repository/wholesaler_repository.dart';
import 'models/wholesaler_models.dart';
import 'components/wholesaler_dashboard_header.dart';
import 'components/wholesaler_stats_card.dart';
import 'components/wholesaler_action_button.dart';
import 'components/wholesaler_item_list.dart';

class WholesalerHomePage extends StatefulWidget {
  const WholesalerHomePage({super.key});

  @override
  State<WholesalerHomePage> createState() => _WholesalerHomePageState();
}

class _WholesalerHomePageState extends State<WholesalerHomePage> {
  late final WholesalerRepository _repository;
  String? wholesalerName;
  int? wholesalerId;

  @override
  void initState() {
    super.initState();
    _repository = WholesalerRepository();
    _repository.addListener(_onRepositoryChanged);
    _loadWholesalerData();
  }

  @override
  void dispose() {
    _repository.removeListener(_onRepositoryChanged);
    super.dispose();
  }

  void _onRepositoryChanged() {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {});
      });
    }
  }

  Future<void> _loadWholesalerData() async {
    final id = await AuthService.getWholesalerId();
    setState(() {
      wholesalerId = id;
      wholesalerName = "لوحة تحكم التاجر";
    });

    // Load all wholesaler data
    await Future.wait([
      _repository.loadProfile(),
      _repository.loadItems(),
      _repository.loadMinCharges(),
    ]);
  }

  Future<void> _logout() async {
    await AuthService.logout();
    _repository.clearData();
    if (mounted) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        AppRoutes.loginOrSignup,
        (route) => false,
      );
    }
  }

  Future<void> _handleRefresh() async {
    await _repository.refreshAll();
  }

  void _navigateToItemManagement() {
    Navigator.pushNamed(context, AppRoutes.wholesalerItemList);
  }

  void _navigateToAddProduct() {
    Navigator.pushNamed(context, AppRoutes.wholesalerItemForm);
  }

  void _navigateToInventoryManagement() {
    Navigator.pushNamed(context, AppRoutes.wholesalerInventoryManagement);
  }

  void _navigateToRegionPricing() {
    Navigator.pushNamed(context, AppRoutes.wholesalerRegionPricing);
  }

  void _navigateToProfile() {
    // TODO: Navigate to profile page
    // ScaffoldMessenger.of(context).showSnackBar(
    //   const SnackBar(content: Text('إدارة الملف الشخصي قريباً')),
    // );
    Navigator.pushNamed(context, AppRoutes.profileEdit);
  }

  void _navigateToAnalytics() {
    // TODO: Navigate to analytics page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التحليلات قريباً')),
    );
  }

  void _navigateToOrders() {
    // TODO: Navigate to orders page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة الطلبات قريباً')),
    );
  }

  void _navigateToSettings() {
    // TODO: Navigate to settings page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الإعدادات قريباً')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: WholesalerAppBar(
        title: 'لوحة تحكم التاجر',
        actions: [
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.logout),
            tooltip: 'تسجيل الخروج',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDefaults.padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Dashboard Header
              WholesalerDashboardHeader(
                profile: _repository.profile,
                isLoading: _repository.isLoadingProfile,
                onProfileTap: _navigateToProfile,
                onLogout: _logout,
              ),
              const SizedBox(height: AppDefaults.padding),

              // Quick Stats
              WholesalerStatsGrid(
                totalProducts: _repository.stats.totalProducts,
                totalOrders: _repository.stats.totalOrders,
                totalRevenue: _repository.stats.totalRevenue,
                totalCustomers: _repository.stats.totalCustomers,
                lowStockItems: _repository.stats.lowStockItems,
                expiredItems: _repository.stats.expiredItems,
                isLoading: _repository.isLoadingItems,
                onProductsTap: _navigateToItemManagement,
                onOrdersTap: _navigateToOrders,
                onRevenueTap: _navigateToAnalytics,
                onCustomersTap: _navigateToAnalytics,
                onLowStockTap: _navigateToInventoryManagement,
                onExpiredTap: _navigateToInventoryManagement,
              ),
              const SizedBox(height: AppDefaults.padding),

              // Quick Actions
              WholesalerQuickActions(
                onAddProduct: _navigateToAddProduct,
                onManageProducts: _navigateToItemManagement,
                onViewOrders: _navigateToOrders,
                onManageInventory: _navigateToInventoryManagement,
                onRegionPricing: _navigateToRegionPricing,
                onAnalytics: _navigateToAnalytics,
                onProfile: _navigateToProfile,
                onSettings: _navigateToSettings,
              ),
              const SizedBox(height: AppDefaults.padding),

              // Recent Items Section
              _buildRecentItemsSection(),
            ],
          ),
        ),
      ),
      floatingActionButton: WholesalerFloatingActionButton(
        onPressed: _navigateToAddProduct,
        tooltip: 'إضافة منتج',
      ),
    );
  }

  Widget _buildRecentItemsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        WholesalerSectionHeader(
          title: 'المنتجات الحديثة',
          subtitle: 'أحدث منتجاتك',
          onActionTap: _navigateToItemManagement,
        ),
        const SizedBox(height: AppDefaults.margin),
        if (_repository.isLoadingItems && _repository.items.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(AppDefaults.padding),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_repository.itemsError != null)
          _buildErrorState(_repository.itemsError!)
        else if (_repository.items.isEmpty)
          _buildEmptyItemsState()
        else
          _buildRecentItemsList(),
      ],
    );
  }

  Widget _buildErrorState(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: AppDefaults.borderRadius,
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل المنتجات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[800],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.red[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _repository.loadItems(forceRefresh: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyItemsState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDefaults.padding * 2),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: AppDefaults.borderRadius,
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.inventory,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات بعد',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف منتجك الأول لبدء عملك في تجارة الجملة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _navigateToAddProduct,
            icon: const Icon(Icons.add),
            label: const Text('إضافة منتج'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentItemsList() {
    // Show only the first 3 items for the dashboard
    final recentItems = _repository.items.take(3).toList();

    return Column(
      children: [
        ...recentItems.map((item) => WholesalerItemCard(
              item: item,
              onTap: () => _navigateToItemManagement(),
              onEdit: () => _navigateToItemManagement(),
              onDelete: () async {
                final confirmed = await _showDeleteConfirmation(item);
                if (confirmed) {
                  await _repository.deleteItem(item.id);
                }
              },
              onExpire: () async {
                final confirmed = await _showExpireConfirmation(item);
                if (confirmed) {
                  await _repository.expireItem(item.id);
                }
              },
              onInventoryUpdate: () => _navigateToInventoryManagement(),
            )),
        if (_repository.items.length > 3) ...[
          const SizedBox(height: AppDefaults.margin),
          TextButton(
            onPressed: _navigateToItemManagement,
            child: Text('عرض جميع المنتجات (${_repository.items.length})'),
          ),
        ],
      ],
    );
  }

  Future<bool> _showDeleteConfirmation(WholesalerItem item) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('حذف المنتج'),
            content: Text('هل أنت متأكد من حذف "${item.product.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<bool> _showExpireConfirmation(WholesalerItem item) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('انتهاء صلاحية المنتج'),
            content: Text(
                'هل أنت متأكد من انتهاء صلاحية تسعير "${item.product.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.orange),
                child: const Text('انتهاء الصلاحية'),
              ),
            ],
          ),
        ) ??
        false;
  }
}
