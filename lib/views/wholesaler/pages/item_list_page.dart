import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../../../core/routes/app_routes.dart';
import '../repository/wholesaler_repository.dart';
import '../models/wholesaler_models.dart';
import '../components/wholesaler_dashboard_header.dart';
import '../components/wholesaler_item_list.dart';
import '../components/wholesaler_action_button.dart';

class ItemListPage extends StatefulWidget {
  const ItemListPage({super.key});

  @override
  State<ItemListPage> createState() => _ItemListPageState();
}

class _ItemListPageState extends State<ItemListPage> {
  late final WholesalerRepository _repository;
  String _searchQuery = '';
  String _filterType = 'all'; // all, low_stock, expired

  @override
  void initState() {
    super.initState();
    _repository = WholesalerRepository();
    _repository.addListener(_onRepositoryChanged);
    _loadItems();
  }

  @override
  void dispose() {
    _repository.removeListener(_onRepositoryChanged);
    super.dispose();
  }

  void _onRepositoryChanged() {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {});
      });
    }
  }

  Future<void> _loadItems() async {
    await _repository.loadItems(forceRefresh: true);
  }

  List<WholesalerItem> get _filteredItems {
    List<WholesalerItem> items = _repository.items;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      items = items
          .where((item) =>
              item.product.name
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              (item.product.barcode
                      ?.toLowerCase()
                      .contains(_searchQuery.toLowerCase()) ??
                  false))
          .toList();
    }

    // Apply type filter
    switch (_filterType) {
      case 'low_stock':
        items = items.where((item) => item.inventoryCount < 10).toList();
        break;
      case 'expired':
        final now = DateTime.now();
        items = items
            .where((item) =>
                item.priceExpiry != null && item.priceExpiry!.isBefore(now))
            .toList();
        break;
      case 'all':
      default:
        // No additional filtering
        break;
    }

    return items;
  }

  Future<void> _navigateToAddItem() async {
    final result =
        await Navigator.pushNamed(context, AppRoutes.wholesalerItemForm);

    if (result == true) {
      // Item was added successfully, refresh the list
      await _loadItems();
    }
  }

  Future<void> _navigateToEditItem(WholesalerItem item) async {
    final result = await Navigator.pushNamed(
      context,
      AppRoutes.wholesalerItemForm,
      arguments: {'item': item},
    );

    if (result == true) {
      // Item was updated successfully, refresh the list
      await _loadItems();
    }
  }

  Future<void> _deleteItem(WholesalerItem item) async {
    final confirmed = await _showDeleteConfirmation(item);
    if (confirmed) {
      final success = await _repository.deleteItem(item.id);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف ${item.product.name} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف ${item.product.name}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _expireItem(WholesalerItem item) async {
    final confirmed = await _showExpireConfirmation(item);
    if (confirmed) {
      final success = await _repository.expireItem(item.id);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('انتهت صلاحية تسعير ${item.product.name}'),
            backgroundColor: Colors.orange,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في انتهاء صلاحية ${item.product.name}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateInventory(WholesalerItem item) async {
    Navigator.pushNamed(context, AppRoutes.wholesalerInventoryManagement);
  }

  Future<bool> _showDeleteConfirmation(WholesalerItem item) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('حذف المنتج'),
            content:
                Text('هل أنت متأكد من رغبتك في حذف "${item.product.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<bool> _showExpireConfirmation(WholesalerItem item) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Expire Product'),
            content: Text(
                'Are you sure you want to expire pricing for "${item.product.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.orange),
                child: const Text('Expire'),
              ),
            ],
          ),
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final filteredItems = _filteredItems;

    return Scaffold(
      appBar: WholesalerAppBar(
        title: 'المنتجات',
        actions: [
          IconButton(
            onPressed: _navigateToAddItem,
            icon: const Icon(Icons.add),
            tooltip: 'إضافة منتج',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: 'البحث عن المنتجات...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: AppDefaults.borderRadius,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      WholesalerActionChip(
                        label: 'الكل (${_repository.items.length})',
                        isSelected: _filterType == 'all',
                        onPressed: () {
                          setState(() {
                            _filterType = 'all';
                          });
                        },
                      ),
                      const SizedBox(width: 8),
                      WholesalerActionChip(
                        label:
                            'مخزون منخفض (${_repository.getLowStockItems().length})',
                        icon: Icons.warning,
                        isSelected: _filterType == 'low_stock',
                        backgroundColor: Colors.orange[50],
                        foregroundColor: Colors.orange[800],
                        onPressed: () {
                          setState(() {
                            _filterType = 'low_stock';
                          });
                        },
                      ),
                      const SizedBox(width: 8),
                      WholesalerActionChip(
                        label:
                            'منتهية الصلاحية (${_repository.getExpiredItems().length})',
                        icon: Icons.schedule,
                        isSelected: _filterType == 'expired',
                        backgroundColor: Colors.red[50],
                        foregroundColor: Colors.red[800],
                        onPressed: () {
                          setState(() {
                            _filterType = 'expired';
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Items List
          Expanded(
            child: WholesalerItemList(
              items: filteredItems,
              isLoading: _repository.isLoadingItems,
              error: _repository.itemsError,
              onRefresh: _loadItems,
              onItemTap: _navigateToEditItem,
              onItemEdit: _navigateToEditItem,
              onItemDelete: _deleteItem,
              onItemExpire: _expireItem,
              onInventoryUpdate: _updateInventory,
            ),
          ),
        ],
      ),
      floatingActionButton: WholesalerFloatingActionButton(
        onPressed: _navigateToAddItem,
        tooltip: 'إضافة منتج',
      ),
    );
  }
}
