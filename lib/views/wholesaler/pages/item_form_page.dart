import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/constants.dart';
import '../repository/wholesaler_repository.dart';
import '../models/wholesaler_models.dart';
import '../components/wholesaler_dashboard_header.dart';
import 'company_selection_page.dart';
import 'product_selection_page.dart';

class ItemFormPage extends StatefulWidget {
  final WholesalerItem? item; // null for create, non-null for edit

  const ItemFormPage({
    super.key,
    this.item,
  });

  @override
  State<ItemFormPage> createState() => _ItemFormPageState();
}

class _ItemFormPageState extends State<ItemFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _basePriceController = TextEditingController();
  final _inventoryCountController = TextEditingController();
  late final WholesalerRepository _repository;

  // Form state
  int? _selectedCompanyId;
  String? _selectedCompanyName;
  int? _selectedProductId;
  String? _selectedProductName;
  DateTime? _priceExpiry;
  bool _isLoading = false;
  String? _error;

  bool get isEditing => widget.item != null;

  @override
  void initState() {
    super.initState();
    _repository = WholesalerRepository();
    _repository.addListener(_onRepositoryChanged);

    if (isEditing) {
      _initializeEditForm();
    }
  }

  @override
  void dispose() {
    _repository.removeListener(_onRepositoryChanged);
    _basePriceController.dispose();
    _inventoryCountController.dispose();
    super.dispose();
  }

  void _onRepositoryChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  void _initializeEditForm() {
    final item = widget.item!;
    _selectedProductId = item.product.id;
    _selectedProductName = item.product.name;
    _basePriceController.text = item.basePrice.toString();
    _inventoryCountController.text = item.inventoryCount.toString();
    _priceExpiry = item.priceExpiry;
  }

  Future<void> _selectCompany() async {
    if (isEditing) return; // Disable in edit mode

    try {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const CompanySelectionPage(),
        ),
      );

      if (result != null && result is Company) {
        setState(() {
          _selectedCompanyId = result.id;
          _selectedCompanyName = result.name;
          // Clear product selection when company changes
          _selectedProductId = null;
          _selectedProductName = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في اختيار الشركة: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _selectProduct() async {
    if (isEditing) return; // Disable in edit mode

    try {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProductSelectionPage(
            selectedCompanyId: _selectedCompanyId,
          ),
        ),
      );

      if (result != null && result is Product) {
        setState(() {
          _selectedProductId = result.id;
          _selectedProductName = result.name;
          // Auto-select company if product has one and no company is selected
          if (_selectedCompanyId == null && result.company != null) {
            _selectedCompanyId = result.company!.id;
            _selectedCompanyName = result.company!.name;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في اختيار المنتج: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _selectPriceExpiry() async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _priceExpiry ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (selectedDate != null) {
      setState(() {
        _priceExpiry = selectedDate;
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedProductId == null && !isEditing) {
      setState(() {
        _error = 'يرجى اختيار منتج';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final basePrice = double.parse(_basePriceController.text);
      final inventoryCount = int.parse(_inventoryCountController.text);

      bool success;
      if (isEditing) {
        // Update existing item
        final request = WholesalerItemUpdateRequest(
          basePrice: basePrice,
          inventoryCount: inventoryCount,
          priceExpiry: _priceExpiry,
        );
        success = await _repository.updateItem(widget.item!.id, request);
      } else {
        // Create new item
        final request = WholesalerItemCreateRequest(
          productId: _selectedProductId!,
          basePrice: basePrice,
          inventoryCount: inventoryCount,
          priceExpiry: _priceExpiry,
        );
        success = await _repository.createItem(request);
      }

      if (success && mounted) {
        Navigator.of(context).pop(true);
      } else {
        setState(() {
          _error = _repository.itemsError ?? 'فشل في حفظ المنتج';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'مدخل غير صحيح: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: WholesalerAppBar(
        title: isEditing ? 'تعديل المنتج' : 'إضافة منتج',
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _submitForm,
              child: Text(
                isEditing ? 'تحديث' : 'حفظ',
                style: const TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDefaults.padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Error display
              if (_error != null) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: AppDefaults.borderRadius,
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _error!,
                          style: TextStyle(color: Colors.red[800]),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppDefaults.padding),
              ],

              // Company Selection
              _buildCompanySelection(),
              const SizedBox(height: AppDefaults.padding),

              // Product Selection
              _buildProductSelection(),
              const SizedBox(height: AppDefaults.padding),

              // Base Price
              _buildBasePriceField(),
              const SizedBox(height: AppDefaults.padding),

              // Inventory Count
              _buildInventoryCountField(),
              const SizedBox(height: AppDefaults.padding),

              // Price Expiry
              _buildPriceExpiryField(),
              const SizedBox(height: AppDefaults.padding * 2),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _submitForm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(isEditing ? 'تحديث المنتج' : 'إضافة منتج'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المنتج *',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: isEditing ? null : _selectProduct, // Disable in edit mode
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              border: Border.all(
                color: isEditing ? Colors.grey[300]! : AppColors.primary,
              ),
              borderRadius: AppDefaults.borderRadius,
              color: isEditing ? Colors.grey[100] : Colors.white,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.inventory,
                  color: isEditing ? Colors.grey[500] : AppColors.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedProductName ?? 'اختر منتج',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: _selectedProductName != null
                              ? (isEditing ? Colors.grey[700] : Colors.black)
                              : Colors.grey[500],
                        ),
                  ),
                ),
                if (!isEditing)
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[400],
                  ),
              ],
            ),
          ),
        ),
        if (isEditing) ...[
          const SizedBox(height: 4),
          Text(
            'لا يمكن تغيير المنتج عند التعديل',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
          ),
        ],
      ],
    );
  }

  Widget _buildCompanySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الشركة (اختياري)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: isEditing ? null : _selectCompany, // Disable in edit mode
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              border: Border.all(
                color: isEditing ? Colors.grey[300]! : AppColors.primary,
              ),
              borderRadius: AppDefaults.borderRadius,
              color: isEditing ? Colors.grey[100] : Colors.white,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.business,
                  color: isEditing ? Colors.grey[500] : AppColors.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedCompanyName ?? 'اختر شركة (اختياري)',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: _selectedCompanyName != null
                              ? (isEditing ? Colors.grey[700] : Colors.black)
                              : Colors.grey[500],
                        ),
                  ),
                ),
                if (!isEditing)
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[400],
                  ),
              ],
            ),
          ),
        ),
        if (isEditing) ...[
          const SizedBox(height: 4),
          Text(
            'لا يمكن تغيير الشركة عند التعديل',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
          ),
        ],
      ],
    );
  }

  Widget _buildBasePriceField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'السعر الأساسي *',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _basePriceController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
          decoration: InputDecoration(
            hintText: 'أدخل السعر الأساسي',
            prefixIcon: const Icon(Icons.attach_money),
            border: OutlineInputBorder(
              borderRadius: AppDefaults.borderRadius,
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'السعر الأساسي مطلوب';
            }
            final price = double.tryParse(value);
            if (price == null || price <= 0) {
              return 'يرجى إدخال سعر صحيح';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildInventoryCountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'عدد المخزون *',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _inventoryCountController,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          decoration: InputDecoration(
            hintText: 'أدخل عدد المخزون',
            prefixIcon: const Icon(Icons.warehouse),
            border: OutlineInputBorder(
              borderRadius: AppDefaults.borderRadius,
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'عدد المخزون مطلوب';
            }
            final count = int.tryParse(value);
            if (count == null || count < 0) {
              return 'يرجى إدخال عدد مخزون صحيح';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPriceExpiryField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'انتهاء صلاحية السعر (اختياري)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectPriceExpiry,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: AppDefaults.borderRadius,
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: AppColors.primary),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _priceExpiry != null
                        ? '${_priceExpiry!.day}/${_priceExpiry!.month}/${_priceExpiry!.year}'
                        : 'اختر تاريخ انتهاء الصلاحية',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: _priceExpiry != null
                              ? Colors.black
                              : Colors.grey[500],
                        ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'اتركه فارغاً لعدم انتهاء الصلاحية',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
      ],
    );
  }
}
