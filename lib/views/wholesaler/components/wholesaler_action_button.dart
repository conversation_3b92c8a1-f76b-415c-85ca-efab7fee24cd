import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';

class WholesalerActionButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onTap;
  final Color? iconColor;
  final Color? backgroundColor;
  final bool isEnabled;

  const WholesalerActionButton({
    super.key,
    required this.title,
    required this.icon,
    required this.onTap,
    this.iconColor,
    this.backgroundColor,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: isEnabled ? 2 : 0,
      borderRadius: AppDefaults.borderRadius,
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: AppDefaults.borderRadius,
        child: Container(
          padding: const EdgeInsets.all(AppDefaults.padding),
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white,
            borderRadius: AppDefaults.borderRadius,
            border: Border.all(
              color: isEnabled ? Colors.grey[300]! : Colors.grey[200]!,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: isEnabled
                    ? (iconColor ?? AppColors.primary)
                    : Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: isEnabled ? null : Colors.grey[400],
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class WholesalerQuickActions extends StatelessWidget {
  final VoidCallback? onManageProducts;
  final VoidCallback? onViewOrders;
  final VoidCallback? onAnalytics;
  final VoidCallback? onSettings;
  final VoidCallback? onAddProduct;
  final VoidCallback? onManageInventory;
  final VoidCallback? onRegionPricing;
  final VoidCallback? onProfile;

  const WholesalerQuickActions({
    super.key,
    this.onManageProducts,
    this.onViewOrders,
    this.onAnalytics,
    this.onSettings,
    this.onAddProduct,
    this.onManageInventory,
    this.onRegionPricing,
    this.onProfile,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: AppDefaults.margin),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.5,
          crossAxisSpacing: AppDefaults.margin,
          mainAxisSpacing: AppDefaults.margin,
          children: [
            WholesalerActionButton(
              title: 'إضافة منتج',
              icon: Icons.add_box,
              iconColor: Colors.green,
              onTap: onAddProduct ?? () {},
              isEnabled: onAddProduct != null,
            ),
            WholesalerActionButton(
              title: 'إدارة المنتجات',
              icon: Icons.inventory_2,
              onTap: onManageProducts ?? () {},
              isEnabled: onManageProducts != null,
            ),
            WholesalerActionButton(
              title: 'عرض الطلبات',
              icon: Icons.receipt,
              iconColor: Colors.blue,
              onTap: onViewOrders ?? () {},
              isEnabled: onViewOrders != null,
            ),
            WholesalerActionButton(
              title: 'المخزون',
              icon: Icons.warehouse,
              iconColor: Colors.orange,
              onTap: onManageInventory ?? () {},
              isEnabled: onManageInventory != null,
            ),
            WholesalerActionButton(
              title: 'أسعار المناطق',
              icon: Icons.location_on,
              iconColor: Colors.purple,
              onTap: onRegionPricing ?? () {},
              isEnabled: onRegionPricing != null,
            ),
            WholesalerActionButton(
              title: 'التحليلات',
              icon: Icons.analytics,
              iconColor: Colors.teal,
              onTap: onAnalytics ?? () {},
              isEnabled: onAnalytics != null,
            ),
            WholesalerActionButton(
              title: 'الملف الشخصي',
              icon: Icons.person,
              iconColor: Colors.indigo,
              onTap: onProfile ?? () {},
              isEnabled: onProfile != null,
            ),
            WholesalerActionButton(
              title: 'الإعدادات',
              icon: Icons.settings,
              iconColor: Colors.grey,
              onTap: onSettings ?? () {},
              isEnabled: onSettings != null,
            ),
          ],
        ),
      ],
    );
  }
}

class WholesalerFloatingActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String tooltip;
  final IconData icon;

  const WholesalerFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.tooltip,
    this.icon = Icons.add,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      child: Icon(icon),
    );
  }
}

class WholesalerActionChip extends StatelessWidget {
  final String label;
  final IconData? icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool isSelected;

  const WholesalerActionChip({
    super.key,
    required this.label,
    this.icon,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 16,
              color: isSelected
                  ? Colors.white
                  : (foregroundColor ?? AppColors.primary),
            ),
            const SizedBox(width: 4),
          ],
          Text(
            label,
            style: TextStyle(
              color: isSelected
                  ? Colors.white
                  : (foregroundColor ?? AppColors.primary),
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
      onPressed: onPressed,
      backgroundColor: isSelected
          ? AppColors.primary
          : (backgroundColor ?? Colors.grey[100]),
      side: BorderSide(
        color: isSelected ? AppColors.primary : Colors.grey[300]!,
      ),
      elevation: isSelected ? 2 : 0,
    );
  }
}
