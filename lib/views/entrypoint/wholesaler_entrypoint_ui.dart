import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_defaults.dart';
import '../wholesaler/wholesaler_home_page.dart';
import '../wholesaler/pages/item_list_page.dart';
import '../wholesaler/profile/wholesaler_profile_page.dart';
import 'components/wholesaler_navigation_bar.dart';

/// This page will contain all the bottom navigation tabs for wholesaler
class WholesalerEntryPointUI extends StatefulWidget {
  const WholesalerEntryPointUI({super.key});

  @override
  State<WholesalerEntryPointUI> createState() => _WholesalerEntryPointUIState();
}

class _WholesalerEntryPointUIState extends State<WholesalerEntryPointUI> {
  /// Current Page
  int currentIndex = 0;

  /// On bottom navigation tap
  void onBottomNavigationTap(int index) {
    currentIndex = index;
    setState(() {});
  }

  /// All the pages
  List<Widget> get pages => [
        const WholesalerHomePage(), // Home
        const WholesalerOrdersPage(), // Orders
        const ItemListPage(canPop: false), // Items
        const WholesalerProfilePage(), // Profile
      ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageTransitionSwitcher(
        transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
          return SharedAxisTransition(
            animation: primaryAnimation,
            secondaryAnimation: secondaryAnimation,
            transitionType: SharedAxisTransitionType.horizontal,
            fillColor: AppColors.scaffoldBackground,
            child: child,
          );
        },
        duration: AppDefaults.duration,
        child: pages[currentIndex],
      ),
      bottomNavigationBar: WholesalerBottomNavigationBar(
        currentIndex: currentIndex,
        onNavTap: onBottomNavigationTap,
      ),
    );
  }
}

/// Placeholder page for Orders
class WholesalerOrdersPage extends StatelessWidget {
  const WholesalerOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الطلبات'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: AppColors.placeholder,
            ),
            SizedBox(height: 16),
            Text(
              'إدارة الطلبات',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.placeholder,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'قريباً',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.placeholder,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
