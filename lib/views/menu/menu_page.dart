import 'package:flutter/material.dart';

import '../../core/constants/constants.dart';
import '../../core/routes/app_routes.dart';
import 'components/category_tile.dart';
import '../../api/products.dart';

class MenuPage extends StatelessWidget {
  const MenuPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.scaffoldBackground,
        title: const Text('القائمة'),
        automaticallyImplyLeading: false,
      ),
      body: FutureBuilder<CategoryCompanyResponse>(
        future: CategoryCompanyApiService.fetchCategoriesAndCompanies(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('خطأ في تحميل البيانات'));
          } else if (!snapshot.hasData) {
            return Center(child: Text('لا توجد بيانات'));
          }
          final data = snapshot.data!;
          return Padding(
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  const Text('الفئات',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                  GridView.count(
                    crossAxisCount: 3,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    children: data.categories.map((category) {
                      return CategoryTile(
                        imageLink:
                            'https://via.placeholder.com/36', // Default placeholder
                        label: category.name,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => CategoryProductPage(
                                id: category.id,
                                type: 'category',
                                name: category.name,
                              ),
                            ),
                          );
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 24),
                  const Text('الشركات',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                  GridView.count(
                    crossAxisCount: 3,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    children: data.companies.map((company) {
                      return CategoryTile(
                        imageLink:
                            'https://via.placeholder.com/36', // Default placeholder
                        label: company.name,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => CategoryProductPage(
                                id: company.id,
                                type: 'company',
                                name: company.name,
                              ),
                            ),
                          );
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

// Details page stub (to be implemented)
class CategoryProductPage extends StatelessWidget {
  final int id;
  final String type; // 'category' or 'company'
  final String name;
  const CategoryProductPage(
      {super.key, required this.id, required this.type, required this.name});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(name)),
      body: Center(child: Text('عرض المنتجات لـ $type: $name (ID: $id)')),
    );
  }
}
