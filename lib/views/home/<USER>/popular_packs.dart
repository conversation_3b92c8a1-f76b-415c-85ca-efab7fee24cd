import 'package:flutter/material.dart';

import '../../../core/components/bundle_tile_square.dart';
import '../../../core/components/product_tile_square.dart';
import '../../../core/components/title_and_action_button.dart';
import '../../../core/constants/constants.dart';
import '../../../core/routes/app_routes.dart';
import '../../../services/home_service.dart';

class PopularPacks extends StatefulWidget {
  const PopularPacks({
    super.key,
  });

  @override
  State<PopularPacks> createState() => _PopularPacksState();
}

class _PopularPacksState extends State<PopularPacks> {
  final HomeService _homeService = HomeService();

  @override
  void initState() {
    super.initState();
    // Load popular products when component initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _homeService.loadPopularProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TitleAndActionButton(
          title: 'المنتجات الشائعة',
          onTap: () => Navigator.pushNamed(context, AppRoutes.popularItems),
        ),
        ListenableBuilder(
          listenable: _homeService,
          builder: (context, child) {
            // Show loading state
            if (_homeService.isLoadingPopularProducts &&
                _homeService.popularProducts.isEmpty) {
              return Container(
                height: 200,
                padding: const EdgeInsets.only(left: AppDefaults.padding),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            // Show error state
            if (_homeService.popularProductsError != null &&
                _homeService.popularProducts.isEmpty) {
              return Container(
                height: 200,
                padding: const EdgeInsets.all(AppDefaults.padding),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 48, color: Colors.grey),
                      const SizedBox(height: 8),
                      Text(
                        'فشل في تحميل المنتجات الشائعة',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () => _homeService.loadPopularProducts(
                            forceRefresh: true),
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
              );
            }

            // Get popular products as UI models
            final popularProducts = _homeService.popularProducts.isNotEmpty
                ? _homeService.popularProductsForUI()
                : Dummy.bundles;

            return SizedBox(
              height: 280,
              child: ListView.separated(
                padding: const EdgeInsets.only(left: AppDefaults.padding),
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  // For popular products, we need to use ProductTileSquare for individual products
                  // and pass the product ID if available
                  if (_homeService.popularProducts.isNotEmpty) {
                    final productId = _homeService.popularProducts[index].id;
                    final productModel =
                        _homeService.popularProducts[index].toProductModel();
                    return ProductTileSquare(
                      data: productModel,
                      productId: productId,
                    );
                  } else {
                    return BundleTileSquare(
                      data: popularProducts[index],
                    );
                  }
                },
                separatorBuilder: (context, index) =>
                    const SizedBox(width: AppDefaults.padding),
                itemCount: popularProducts.length,
              ),
            );
          },
        ),
      ],
    );
  }
}
