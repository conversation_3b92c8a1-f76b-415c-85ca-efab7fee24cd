import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../core/constants/constants.dart';
import '../../../core/components/network_image.dart';
import '../../../api/products.dart';

class WholesalerSelectionDialog extends StatefulWidget {
  final List<ProductPriceInfo> wholesalerPrices;
  final String productName;
  final Function(ProductPriceInfo selectedWholesaler) onWholesalerSelected;

  const WholesalerSelectionDialog({
    super.key,
    required this.wholesalerPrices,
    required this.productName,
    required this.onWholesalerSelected,
  });

  @override
  State<WholesalerSelectionDialog> createState() =>
      _WholesalerSelectionDialogState();
}

class _WholesalerSelectionDialogState extends State<WholesalerSelectionDialog> {
  ProductPriceInfo? selectedWholesaler;

  @override
  void initState() {
    super.initState();
    // Pre-select the cheapest option
    if (widget.wholesalerPrices.isNotEmpty) {
      selectedWholesaler = widget.wholesalerPrices.first;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'اختر التاجر',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.productName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    shape: const CircleBorder(),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Info text
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'يمكنك إضافة منتجات من نفس التاجر فقط في السلة الواحدة',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.primary,
                          ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Wholesaler list
            ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 300),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: widget.wholesalerPrices.length,
                itemBuilder: (context, index) {
                  final wholesaler = widget.wholesalerPrices[index];
                  final isSelected = selectedWholesaler == wholesaler;
                  final isLowestPrice = index == 0; // Assuming sorted by price

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedWholesaler = wholesaler;
                      });
                    },
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isSelected
                              ? AppColors.primary
                              : Colors.grey[300]!,
                          width: isSelected ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        color: isSelected
                            ? AppColors.primary.withOpacity(0.05)
                            : null,
                      ),
                      child: Row(
                        children: [
                          // Wholesaler logo
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.grey[200],
                            ),
                            child: wholesaler.wholesaler.logo != null
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: NetworkImageWithLoader(
                                      wholesaler.wholesaler.logo!,
                                      fit: BoxFit.cover,
                                    ),
                                  )
                                : Center(
                                    child: SvgPicture.asset(
                                      AppIcons.shoppingBag,
                                      width: 24,
                                      height: 24,
                                      colorFilter: ColorFilter.mode(
                                        Colors.grey[400]!,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                          ),

                          const SizedBox(width: 12),

                          // Wholesaler info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        wholesaler.wholesaler.title,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                                    ),
                                    if (isLowestPrice)
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.green,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          'أفضل سعر',
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelSmall
                                              ?.copyWith(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  wholesaler.wholesaler.category,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Text(
                                      'المخزون: ${wholesaler.inventoryCount}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: wholesaler.inventoryCount > 0
                                                ? Colors.green
                                                : Colors.red,
                                          ),
                                    ),
                                    if (wholesaler.priceExpiry != null) ...[
                                      const SizedBox(width: 8),
                                      Text(
                                        'ينتهي العرض قريباً',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall
                                            ?.copyWith(
                                              color: Colors.orange,
                                            ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(width: 12),

                          // Price and selection indicator
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '${wholesaler.price.toStringAsFixed(2)} ج.م',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primary,
                                    ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                width: 20,
                                height: 20,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isSelected
                                        ? AppColors.primary
                                        : Colors.grey[400]!,
                                    width: 2,
                                  ),
                                  color: isSelected
                                      ? AppColors.primary
                                      : Colors.transparent,
                                ),
                                child: isSelected
                                    ? const Icon(
                                        Icons.check,
                                        size: 14,
                                        color: Colors.white,
                                      )
                                    : null,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: selectedWholesaler != null
                        ? () {
                            widget.onWholesalerSelected(selectedWholesaler!);
                            Navigator.of(context).pop();
                          }
                        : null,
                    child: const Text('إضافة إلى السلة'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show the wholesaler selection dialog
Future<void> showWholesalerSelectionDialog({
  required BuildContext context,
  required List<ProductPriceInfo> wholesalerPrices,
  required String productName,
  required Function(ProductPriceInfo selectedWholesaler) onWholesalerSelected,
}) {
  return showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => WholesalerSelectionDialog(
      wholesalerPrices: wholesalerPrices,
      productName: productName,
      onWholesalerSelected: onWholesalerSelected,
    ),
  );
}
