# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via httpx
    # via openai
asgiref==3.8.1
    # via django
    # via django-cors-headers
boto3==1.37.27
    # via django-storages
botocore==1.37.27
    # via boto3
    # via s3transfer
certifi==2025.1.31
    # via httpcore
    # via httpx
click==8.1.8
    # via uvicorn
distro==1.9.0
    # via openai
django==5.2
    # via django-cors-headers
    # via django-extensions
    # via django-ninja
    # via django-redis
    # via django-safedelete
    # via django-storages
django-cors-headers==4.7.0
django-extensions==3.2.3
django-ninja==1.3.0
django-redis==5.4.0
django-safedelete==1.4.1
django-storages==1.14.6
dnspython==2.7.0
    # via email-validator
dotenv==0.9.9
email-validator==2.2.0
    # via pydantic
factory-boy==3.3.3
faker==37.4.0
    # via factory-boy
h11==0.14.0
    # via httpcore
    # via uvicorn
httpcore==1.0.8
    # via httpx
httpx==0.28.1
    # via openai
    # via upstash-vector
idna==3.10
    # via anyio
    # via email-validator
    # via httpx
jiter==0.9.0
    # via openai
jmespath==1.0.1
    # via boto3
    # via botocore
openai==1.75.0
packaging==24.2
    # via django-safedelete
pillow==11.1.0
psycopg2-binary==2.9.10
pydantic==2.11.2
    # via django-ninja
    # via openai
pydantic-core==2.33.1
    # via pydantic
pyjwt==2.10.1
python-dateutil==2.9.0.post0
    # via botocore
python-dotenv==1.1.0
    # via dotenv
redis==5.2.1
    # via django-redis
s3transfer==0.11.4
    # via boto3
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
    # via openai
sqlparse==0.5.3
    # via django
tqdm==4.67.1
    # via openai
typing-extensions==4.13.0
    # via anyio
    # via openai
    # via pydantic
    # via pydantic-core
    # via typing-inspection
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via faker
upstash-vector==0.8.0
urllib3==2.3.0
    # via botocore
uvicorn==0.34.0
whitenoise==6.9.0
