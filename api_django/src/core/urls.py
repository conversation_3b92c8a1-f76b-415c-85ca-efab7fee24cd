"""
URL configuration for core project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.http import HttpResponse
from django.urls import path
from ninja import NinjaAPI

from products.views import router as products_router
from products.home_views import router as home_router
from accounts.urls import router as accounts_router
from wholesalers.views import router as wholesalers_router
from api.urls import router as api_router
from .api_docs import register_docs_endpoints


def index(request):
    return HttpResponse("Hello, World!")


api = NinjaAPI()
api.add_router("", products_router)
api.add_router("home/", home_router)  # Home screen endpoints
api.add_router("accounts/", accounts_router)
api.add_router("wholesalers/", wholesalers_router)


# Register documentation endpoints
register_docs_endpoints(api)


api.add_router("v2/", api_router)


@api.get("/")
def test(request):
    # test redis
    from django_redis import get_redis_connection

    redis = get_redis_connection("default")
    redis.set("test", "test")
    return {"message": "Hello, World!"}


urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/", api.urls),
    # path("api/v2/", .urls),
    # path("", index_view, name="index"),
    # # HTML views for companies
    # path("companies/", company_list_view, name="company_list"),
    # path("companies/create/", company_create_view, name="company_create"),
    # # HTML views for categories
    # path("categories/", category_list_view, name="category_list"),
    # path("categories/create/", category_create_view, name="category_create"),
    # path("categories/<int:category_id>/", category_detail_view, name="category_detail"),
    # # HTML views for products
    # path("products/", product_list_view, name="product_list"),
    # path("products/create/", product_create_view, name="product_create"),
    # path("products/<int:product_id>/", product_detail_view, name="product_detail"),
    # path(
    #     "products/<int:product_id>/update/", product_update_view, name="product_update"
    # ),
]
