import logging
from firebase_admin import messaging

from accounts.models import CustomUser, Notification


logger = logging.getLogger(__name__)


def send_notification(user_id: int, title: str, body: str) -> bool:
    """
    Send a notifcation via Firebase FCM
    """
    user = CustomUser.objects.get(id=user_id)
    if not user.fcm_token:
        return False

    try:
        message = messaging.Message(
            notification=messaging.Notification(
                title=title,
                body=body,
            ),
            token=user.fcm_token,
        )
        response = messaging.send(message)
        if response:
            Notification.objects.create(
                user=user,
                title=title,
                body=body,
                is_read=False,
            )
            return True
    except Exception as e:
        logger.error(f"Error sending notification to user {user_id}: {e}")
        return False
