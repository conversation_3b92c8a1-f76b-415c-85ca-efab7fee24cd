from django.contrib import admin
from .models import Store, Order, OrderItem


class StoreAdmin(admin.ModelAdmin):
    list_display = ("name", "owner", "city", "state", "country", "created_at")
    list_filter = ("country", "state", "city", "created_at")
    search_fields = (
        "name",
        "description",
        "address",
        "owner__username",
        "owner__email",
        "owner__phone",
    )
    raw_id_fields = ("owner",)
    autocomplete_fields = ["city", "state", "country"]
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (None, {"fields": ("name", "owner", "description")}),
        (
            "Location",
            {
                "fields": ("address", "city", "state", "country"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


# Register the model with the admin site
admin.site.register(Store, StoreAdmin)


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 1
    readonly_fields = ("product_item", "quantity", "price_per_unit", "total_price")


class OrderAdmin(admin.ModelAdmin):
    list_display = ("id", "store", "wholesaler", "created_at")
    list_filter = ("store", "wholesaler", "created_at")
    search_fields = ("store__name", "wholesaler__name")
    raw_id_fields = ("store", "wholesaler")
    autocomplete_fields = ["store", "wholesaler"]
    readonly_fields = (
        "created_at",
        "updated_at",
        "deleted_at",
        "status_updated_at",
        "status_updated_by",
        "products_total_price",
        "products_total_quantity",
        "products_total_price",
        "products_total_quantity",
        "products_total_price",
        "total_price",
        "fees",
    )
    inlines = [OrderItemInline]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "store",
                    "wholesaler",
                    "total_price",
                    "fees",
                    "deliver_at",
                    "status",
                    "status_reason",
                    "status_updated_at",
                    "status_updated_by",
                    "products_total_price",
                    "products_total_quantity",
                )
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


admin.site.register(Order, OrderAdmin)
