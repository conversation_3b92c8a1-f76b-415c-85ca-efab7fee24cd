import jwt
from ninja.security import <PERSON>ttp<PERSON>earer

from core import settings
from accounts.models import CustomUser


class AuthBearerMiddleware(HttpBearer):
    async def authenticate(self, request, token):
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            user_id = payload.get("user_id")
            if user_id is None:
                return None

            user = await CustomUser.objects.aget(id=user_id)
            request.user = user
            return user
        except jwt.ExpiredSignatureError:
            print("Token has expired")
            return None
        except jwt.InvalidAlgorithmError:
            print("Invalid algorithm used in token")
            return None
        except jwt.DecodeError:
            print("Token is malformed or signature is invalid")
            return None
        except jwt.PyJWTError as e:
            print(f"JWT Error: {e}")
            return None
        except CustomUser.DoesNotExist:
            print("User does not exist")
            return None


AuthMiddleware = AuthBearerMiddleware()

# Usage example for protected endpoints:
# @router.get("/some-endpoint", auth=auth)
# async def protected_endpoint(request):
#     # request.user will contain the authenticated user
#     return {"user": request.user.username}
