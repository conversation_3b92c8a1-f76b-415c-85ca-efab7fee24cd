# POST: /api/v2/wholesaler-orders/accept/{order_id}
from datetime import datetime
from typing import Optional

from ninja import Schema, Field
from django.shortcuts import get_object_or_404
from django.db import transaction as db_transaction
from django.utils import timezone
from accounts.models import Wallet, Transaction, TransactionActionEnum
from accounts.utils import send_notification
from api.stores import StoreOut
from stores.models import Order, OrderStatus
from wholesalers.models import Wholesaler


class WholesalerOrderOut(Schema):
    id: int
    store: StoreOut
    total_price: float
    fees: float
    deliver_at: datetime
    products_total_price: float
    products_total_quantity: int
    status: OrderStatus
    status_reason: str
    status_updated_at: datetime
    status_updated_by: int
    completed_at: datetime
    final_completed_price: float


class WholesalerOrdersListOut(Schema):
    orders: list[WholesalerOrderOut]


def list_my_wholesaler_orders(
    request, offset: int = 0, limit: int = 10
) -> WholesalerOrdersListOut:
    wholesaler = get_object_or_404(
        Wholesaler, user=request.auth, deleted_at__isnull=True
    )
    orders = Order.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).order_by("-created_at")[offset : offset + limit]
    return {"orders": orders}


# POST: /api/v2/wholesaler-orders/accept/{order_id}
def accept_order(request, order_id: int):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order, id=order_id, wholesaler__user=request.auth, deleted_at__isnull=True
        )
        if order.status != OrderStatus.PENDING:
            return {"error": "Order is not pending."}
        order.status = OrderStatus.PROCESSING
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.save()
        # Notify reseller
        send_notification(
            order.store.owner.id,
            "تم قبول الطلب",
            f"تم قبول طلبك رقم {order.id} من قبل المورد.",
        )
        return {"success": True}


# POST: /api/v2/wholesaler-orders/reject/{order_id}
def reject_order(request, order_id: int):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order, id=order_id, wholesaler__user=request.auth, deleted_at__isnull=True
        )
        if order.status not in [OrderStatus.PENDING, OrderStatus.PROCESSING]:
            return {"error": "Order cannot be rejected in its current status."}
        order.status = OrderStatus.CANCELLED
        order.status_reason = "Rejected by wholesaler"
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.completed_at = timezone.now()
        order.save()
        # Deduct fees from wholesaler wallet
        wholesaler_wallet = Wallet.objects.get(user=request.auth)
        fee = order.fees
        if wholesaler_wallet.balance < fee:
            return {"error": "Insufficient balance to pay fees."}
        wholesaler_wallet.balance -= fee
        wholesaler_wallet.save()
        Transaction.objects.create(
            related_order=order,
            user=request.auth,
            wallet=wholesaler_wallet,
            action=TransactionActionEnum.FEES,
            amount=fee,
        )
        # Notify reseller
        send_notification(
            order.store.owner.id,
            "تم رفض الطلب",
            f"تم رفض طلبك رقم {order.id} من قبل المورد.",
        )
        return {"success": True}


# POST: /api/v2/wholesaler-orders/cancel/{order_id}
def cancel_order(request, order_id: int):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order, id=order_id, wholesaler__user=request.auth, deleted_at__isnull=True
        )
        if order.status not in [OrderStatus.PENDING, OrderStatus.PROCESSING]:
            return {"error": "Order cannot be cancelled in its current status."}
        order.status = OrderStatus.CANCELLED
        order.status_reason = "Cancelled by wholesaler"
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.completed_at = timezone.now()
        order.save()
        # Deduct fees from wholesaler wallet
        wholesaler_wallet = Wallet.objects.get(user=request.auth)
        fee = order.fees
        if wholesaler_wallet.balance < fee:
            return {"error": "Insufficient balance to pay fees."}
        wholesaler_wallet.balance -= fee
        wholesaler_wallet.save()
        Transaction.objects.create(
            related_order=order,
            user=request.auth,
            wallet=wholesaler_wallet,
            action=TransactionActionEnum.FEES,
            amount=fee,
        )
        # Notify reseller
        send_notification(
            order.store.owner.id,
            "تم إلغاء الطلب",
            f"تم إلغاء طلبك رقم {order.id} من قبل المورد.",
        )
        return {"success": True}


class OrderCompleteIn(Schema):
    order_id: int = Field(..., description="Order ID")
    final_completed_price: Optional[float] = Field(
        ..., description="Final completed price of the order"
    )


# POST: /api/v2/wholesaler-orders/complete
def complete_order(request, order_in: OrderCompleteIn):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order,
            id=order_in.order_id,
            wholesaler__user=request.auth,
            deleted_at__isnull=True,
        )
        if order.status not in [OrderStatus.PROCESSING]:
            return {"error": "Order cannot be completed in its current status."}
        order.status = OrderStatus.DELIVERED
        order.final_completed_price = (
            order_in.final_completed_price or order.total_price
        )
        order.completed_at = timezone.now()
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.save()
        # Deduct fees from wholesaler wallet
        wholesaler_wallet = Wallet.objects.get(user=request.auth)
        fee = order.fees
        if wholesaler_wallet.balance < fee:
            return {"error": "Insufficient balance to pay fees."}
        wholesaler_wallet.balance -= fee
        wholesaler_wallet.save()
        Transaction.objects.create(
            related_order=order,
            user=request.auth,
            wallet=wholesaler_wallet,
            action=TransactionActionEnum.FEES,
            amount=fee,
        )
        # Notify reseller
        send_notification(
            order.store.owner.id,
            "تم اكتمال الطلب",
            f"تم اكتمال طلبك رقم {order.id} من قبل المورد.",
        )
        return {"success": True}
